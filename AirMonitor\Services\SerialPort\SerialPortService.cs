using System.IO;
using System.IO.Ports;
using System.Management;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Logging;
using AirMonitor.Models;

namespace AirMonitor.Services;

/// <summary>
/// 串口服务实现
/// </summary>
public class SerialPortService : ISerialPortService, IDisposable
{
    #region 字段

    private readonly ILogger<SerialPortService> _logger;
    private readonly IConfigurationService _configurationService;
    private SerialPort? _serialPort;
    private SerialPortConnectionConfig _currentConnectionConfig;
    private readonly Timer _portMonitorTimer;
    private List<string> _lastAvailablePorts = new();
    private bool _disposed = false;

    #endregion

    #region 事件

    public event EventHandler<SerialPortConnectionStatusChangedEventArgs>? ConnectionStatusChanged;
    public event EventHandler<SerialPortListChangedEventArgs>? PortListChanged;
    public event EventHandler<SerialPortDataReceivedEventArgs>? DataReceived;

    #endregion

    #region 属性

    public SerialPortConnectionConfig CurrentConnectionConfig => _currentConnectionConfig;
    public SerialPortConnectionStatus ConnectionStatus => _currentConnectionConfig.ConnectionStatus;
    public bool IsConnected => _serialPort?.IsOpen == true;

    #endregion

    #region 构造函数

    public SerialPortService(ILogger<SerialPortService> logger, IConfigurationService configurationService)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _configurationService = configurationService ?? throw new ArgumentNullException(nameof(configurationService));
        
        _currentConnectionConfig = new SerialPortConnectionConfig();
        
        // 创建端口监控定时器（每2秒检查一次）
        _portMonitorTimer = new Timer(OnPortMonitorTimer, null, Timeout.Infinite, Timeout.Infinite);
        
        _logger.LogDebug("串口服务已初始化");
    }

    #endregion

    #region ISerialPortService 实现

    /// <summary>
    /// 获取可用串口列表
    /// </summary>
    public async Task<List<SerialPortInfo>> GetAvailablePortsAsync()
    {
        return await Task.Run(() =>
        {
            try
            {
                var portInfos = new List<SerialPortInfo>();
                var portNames = SerialPort.GetPortNames();

                foreach (var portName in portNames)
                {
                    var portInfo = new SerialPortInfo
                    {
                        PortName = portName,
                        Description = GetPortDescription(portName),
                        IsAvailable = true, // 默认设为可用，让用户尝试连接
                        IsConnected = _currentConnectionConfig.SelectedPortName == portName && IsConnected
                    };

                    // 检查端口是否可用（但不阻止用户尝试连接）
                    var isAvailable = CheckPortAvailability(portName);
                    portInfo.IsAvailable = isAvailable;

                    // 记录调试信息
                    _logger.LogDebug("串口 {PortName} 可用性检查结果: {IsAvailable}", portName, isAvailable);

                    portInfos.Add(portInfo);
                }

                _logger.LogDebug("获取到 {Count} 个串口", portInfos.Count);
                return portInfos.OrderBy(p => p.PortName).ToList();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可用串口列表失败");
                return new List<SerialPortInfo>();
            }
        });
    }

    /// <summary>
    /// 检查串口是否可用
    /// </summary>
    public async Task<bool> IsPortAvailableAsync(string portName)
    {
        return await Task.Run(() => CheckPortAvailability(portName));
    }

    /// <summary>
    /// 连接串口
    /// </summary>
    public async Task<bool> ConnectAsync(string portName, SerialPortProtocolType protocolType)
    {
        try
        {
            if (IsConnected)
            {
                _logger.LogWarning("串口已连接，请先断开连接");
                return false;
            }

            UpdateConnectionStatus(SerialPortConnectionStatus.Connecting);

            var protocolConfig = SerialPortProtocolConfig.GetPredefinedProtocols()
                .FirstOrDefault(p => p.ProtocolType == protocolType);

            if (protocolConfig == null)
            {
                _logger.LogError("未找到协议配置: {ProtocolType}", protocolType);
                UpdateConnectionStatus(SerialPortConnectionStatus.ConnectionFailed, "未找到协议配置");
                return false;
            }

            // 检查端口是否可用
            if (!await IsPortAvailableAsync(portName))
            {
                _logger.LogError("串口 {PortName} 不可用或被占用", portName);
                UpdateConnectionStatus(SerialPortConnectionStatus.ConnectionFailed, "串口不可用或被占用");
                return false;
            }

            // 创建串口连接
            _serialPort = new SerialPort(portName)
            {
                BaudRate = protocolConfig.BaudRate,
                DataBits = protocolConfig.DataBits,
                StopBits = (StopBits)protocolConfig.StopBits,
                Parity = ParseParity(protocolConfig.Parity),
                ReadTimeout = 1000,
                WriteTimeout = 1000
            };

            // 订阅数据接收事件
            _serialPort.DataReceived += OnSerialPortDataReceived;

            // 打开串口
            await Task.Run(() => _serialPort.Open());

            // 更新连接配置
            _currentConnectionConfig.SelectedPortName = portName;
            _currentConnectionConfig.SelectedProtocolType = protocolType;
            _currentConnectionConfig.LastConnectedTime = DateTime.Now;

            UpdateConnectionStatus(SerialPortConnectionStatus.Connected);

            _logger.LogInformation("串口连接成功: {PortName}, 协议: {Protocol}, 波特率: {BaudRate}", 
                portName, protocolConfig.Name, protocolConfig.BaudRate);

            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "连接串口失败: {PortName}", portName);
            UpdateConnectionStatus(SerialPortConnectionStatus.ConnectionFailed, ex.Message);
            
            // 清理资源
            _serialPort?.Dispose();
            _serialPort = null;
            
            return false;
        }
    }

    /// <summary>
    /// 断开串口连接
    /// </summary>
    public async Task<bool> DisconnectAsync()
    {
        try
        {
            if (!IsConnected)
            {
                _logger.LogWarning("串口未连接");
                return true;
            }

            UpdateConnectionStatus(SerialPortConnectionStatus.Disconnecting);

            await Task.Run(() =>
            {
                if (_serialPort != null)
                {
                    _serialPort.DataReceived -= OnSerialPortDataReceived;
                    _serialPort.Close();
                    _serialPort.Dispose();
                    _serialPort = null;
                }
            });

            UpdateConnectionStatus(SerialPortConnectionStatus.Disconnected);

            _logger.LogInformation("串口连接已断开: {PortName}", _currentConnectionConfig.SelectedPortName);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "断开串口连接失败");
            UpdateConnectionStatus(SerialPortConnectionStatus.ConnectionFailed, ex.Message);
            return false;
        }
    }

    /// <summary>
    /// 发送数据
    /// </summary>
    public async Task<bool> SendDataAsync(byte[] data)
    {
        try
        {
            if (!IsConnected || _serialPort == null)
            {
                _logger.LogWarning("串口未连接，无法发送数据");
                return false;
            }

            await Task.Run(() => _serialPort.Write(data, 0, data.Length));
            
            _logger.LogDebug("发送数据成功: {Length} 字节", data.Length);
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送数据失败");
            return false;
        }
    }

    /// <summary>
    /// 发送字符串数据
    /// </summary>
    public async Task<bool> SendStringAsync(string data)
    {
        try
        {
            var bytes = Encoding.UTF8.GetBytes(data);
            return await SendDataAsync(bytes);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "发送字符串数据失败");
            return false;
        }
    }

    /// <summary>
    /// 开始监控串口列表变化
    /// </summary>
    public void StartPortMonitoring()
    {
        try
        {
            _portMonitorTimer.Change(0, 2000); // 立即开始，每2秒检查一次
            _logger.LogDebug("串口监控已启动");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "启动串口监控失败");
        }
    }

    /// <summary>
    /// 停止监控串口列表变化
    /// </summary>
    public void StopPortMonitoring()
    {
        try
        {
            _portMonitorTimer.Change(Timeout.Infinite, Timeout.Infinite);
            _logger.LogDebug("串口监控已停止");
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "停止串口监控失败");
        }
    }

    /// <summary>
    /// 保存连接配置
    /// </summary>
    public async Task<bool> SaveConnectionConfigAsync(SerialPortConnectionConfig config)
    {
        try
        {
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "serialport-config.json");
            var json = JsonSerializer.Serialize(config, new JsonSerializerOptions { WriteIndented = true });
            await File.WriteAllTextAsync(configPath, json);
            
            _logger.LogDebug("串口连接配置已保存");
            return true;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "保存串口连接配置失败");
            return false;
        }
    }

    /// <summary>
    /// 加载连接配置
    /// </summary>
    public async Task<SerialPortConnectionConfig> LoadConnectionConfigAsync()
    {
        try
        {
            var configPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "serialport-config.json");
            if (!File.Exists(configPath))
            {
                return new SerialPortConnectionConfig();
            }

            var json = await File.ReadAllTextAsync(configPath);
            var config = JsonSerializer.Deserialize<SerialPortConnectionConfig>(json);
            
            if (config != null)
            {
                // 重置连接状态（配置文件中的连接状态不应该被保持）
                config.ConnectionStatus = SerialPortConnectionStatus.Disconnected;
                _currentConnectionConfig = config;
                
                _logger.LogDebug("串口连接配置已加载");
                return config;
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "加载串口连接配置失败");
        }

        return new SerialPortConnectionConfig();
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 获取串口描述信息
    /// </summary>
    private string GetPortDescription(string portName)
    {
        try
        {
            using var searcher = new ManagementObjectSearcher(
                "SELECT * FROM Win32_PnPEntity WHERE Caption LIKE '%(COM%'");

            foreach (ManagementObject obj in searcher.Get())
            {
                var caption = obj["Caption"]?.ToString();
                if (caption != null && caption.Contains($"({portName})"))
                {
                    return caption.Replace($"({portName})", "").Trim();
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogDebug(ex, "获取串口描述失败: {PortName}", portName);
        }

        return string.Empty;
    }

    /// <summary>
    /// 检查端口可用性
    /// </summary>
    private bool CheckPortAvailability(string portName)
    {
        try
        {
            using var testPort = new SerialPort(portName);
            testPort.Open();
            testPort.Close();
            return true;
        }
        catch
        {
            return false;
        }
    }

    /// <summary>
    /// 解析校验位
    /// </summary>
    private Parity ParseParity(string parity)
    {
        return parity.ToUpper() switch
        {
            "N" or "NONE" => Parity.None,
            "E" or "EVEN" => Parity.Even,
            "O" or "ODD" => Parity.Odd,
            "M" or "MARK" => Parity.Mark,
            "S" or "SPACE" => Parity.Space,
            _ => Parity.None
        };
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    private void UpdateConnectionStatus(SerialPortConnectionStatus newStatus, string? errorMessage = null)
    {
        var oldStatus = _currentConnectionConfig.ConnectionStatus;
        _currentConnectionConfig.ConnectionStatus = newStatus;

        ConnectionStatusChanged?.Invoke(this, new SerialPortConnectionStatusChangedEventArgs
        {
            OldStatus = oldStatus,
            NewStatus = newStatus,
            PortName = _currentConnectionConfig.SelectedPortName,
            ErrorMessage = errorMessage
        });

        _logger.LogDebug("串口连接状态变化: {OldStatus} -> {NewStatus}", oldStatus, newStatus);
    }

    /// <summary>
    /// 端口监控定时器回调
    /// </summary>
    private async void OnPortMonitorTimer(object? state)
    {
        try
        {
            var currentPorts = SerialPort.GetPortNames().ToList();

            // 检查是否有变化
            var addedPorts = currentPorts.Except(_lastAvailablePorts).ToList();
            var removedPorts = _lastAvailablePorts.Except(currentPorts).ToList();

            if (addedPorts.Any() || removedPorts.Any())
            {
                _lastAvailablePorts = currentPorts;

                var availablePorts = await GetAvailablePortsAsync();

                PortListChanged?.Invoke(this, new SerialPortListChangedEventArgs
                {
                    AvailablePorts = availablePorts,
                    AddedPorts = addedPorts,
                    RemovedPorts = removedPorts
                });

                _logger.LogDebug("串口列表变化 - 新增: {Added}, 移除: {Removed}",
                    string.Join(",", addedPorts), string.Join(",", removedPorts));

                // 检查当前连接的串口是否被移除
                if (IsConnected && removedPorts.Contains(_currentConnectionConfig.SelectedPortName))
                {
                    _logger.LogWarning("当前连接的串口 {PortName} 已被移除", _currentConnectionConfig.SelectedPortName);
                    await DisconnectAsync();

                    // 如果启用了自动重连，尝试重连
                    if (_currentConnectionConfig.AutoReconnect && addedPorts.Contains(_currentConnectionConfig.SelectedPortName))
                    {
                        _logger.LogInformation("检测到串口重新插入，尝试自动重连: {PortName}", _currentConnectionConfig.SelectedPortName);
                        await Task.Delay(1000); // 等待1秒让系统稳定
                        await ConnectAsync(_currentConnectionConfig.SelectedPortName, _currentConnectionConfig.SelectedProtocolType);
                    }
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "端口监控检查失败");
        }
    }

    /// <summary>
    /// 串口数据接收事件处理
    /// </summary>
    private void OnSerialPortDataReceived(object sender, SerialDataReceivedEventArgs e)
    {
        try
        {
            if (_serialPort == null || !_serialPort.IsOpen)
                return;

            var bytesToRead = _serialPort.BytesToRead;
            if (bytesToRead == 0)
                return;

            var buffer = new byte[bytesToRead];
            var bytesRead = _serialPort.Read(buffer, 0, bytesToRead);

            if (bytesRead > 0)
            {
                var actualData = new byte[bytesRead];
                Array.Copy(buffer, actualData, bytesRead);

                var dataAsString = Encoding.UTF8.GetString(actualData);

                DataReceived?.Invoke(this, new SerialPortDataReceivedEventArgs
                {
                    Data = actualData,
                    DataAsString = dataAsString,
                    ReceivedTime = DateTime.Now,
                    PortName = _currentConnectionConfig.SelectedPortName
                });

                _logger.LogDebug("接收到串口数据: {Length} 字节", bytesRead);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "处理串口数据接收失败");
        }
    }

    #endregion

    #region IDisposable 实现

    public void Dispose()
    {
        Dispose(true);
        GC.SuppressFinalize(this);
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposed)
        {
            if (disposing)
            {
                StopPortMonitoring();
                _portMonitorTimer?.Dispose();

                if (_serialPort != null)
                {
                    _serialPort.DataReceived -= OnSerialPortDataReceived;
                    if (_serialPort.IsOpen)
                    {
                        _serialPort.Close();
                    }
                    _serialPort.Dispose();
                    _serialPort = null;
                }
            }

            _disposed = true;
        }
    }

    #endregion
}

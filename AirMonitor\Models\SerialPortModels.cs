using System.ComponentModel;

namespace AirMonitor.Models;

/// <summary>
/// 串口协议类型枚举
/// </summary>
public enum SerialPortProtocolType
{
    /// <summary>
    /// 商用内外机通讯协议（波特率：9600）
    /// </summary>
    [Description("商用内外机通讯协议")]
    CommercialProtocol = 0,

    /// <summary>
    /// 模块机通讯协议（波特率：4800）
    /// </summary>
    [Description("模块机通讯协议")]
    ModuleProtocol = 1
}

/// <summary>
/// 串口协议配置
/// </summary>
public class SerialPortProtocolConfig
{
    /// <summary>
    /// 协议类型
    /// </summary>
    public SerialPortProtocolType ProtocolType { get; set; }

    /// <summary>
    /// 协议名称
    /// </summary>
    public string Name { get; set; } = string.Empty;

    /// <summary>
    /// 波特率
    /// </summary>
    public int BaudRate { get; set; }

    /// <summary>
    /// 数据位
    /// </summary>
    public int DataBits { get; set; } = 8;

    /// <summary>
    /// 停止位
    /// </summary>
    public int StopBits { get; set; } = 1;

    /// <summary>
    /// 校验位（N=无校验）
    /// </summary>
    public string Parity { get; set; } = "N";

    /// <summary>
    /// 获取预定义的协议配置
    /// </summary>
    public static List<SerialPortProtocolConfig> GetPredefinedProtocols()
    {
        return new List<SerialPortProtocolConfig>
        {
            new SerialPortProtocolConfig
            {
                ProtocolType = SerialPortProtocolType.CommercialProtocol,
                Name = "商用内外机通讯协议",
                BaudRate = 9600,
                DataBits = 8,
                StopBits = 1,
                Parity = "N"
            },
            new SerialPortProtocolConfig
            {
                ProtocolType = SerialPortProtocolType.ModuleProtocol,
                Name = "模块机通讯协议",
                BaudRate = 4800,
                DataBits = 8,
                StopBits = 1,
                Parity = "N"
            }
        };
    }
}

/// <summary>
/// 串口信息
/// </summary>
public class SerialPortInfo
{
    /// <summary>
    /// 串口名称（如COM1）
    /// </summary>
    public string PortName { get; set; } = string.Empty;

    /// <summary>
    /// 串口描述
    /// </summary>
    public string Description { get; set; } = string.Empty;

    /// <summary>
    /// 是否可用（未被占用）
    /// </summary>
    public bool IsAvailable { get; set; } = true;

    /// <summary>
    /// 是否已连接
    /// </summary>
    public bool IsConnected { get; set; } = false;

    /// <summary>
    /// 显示名称
    /// </summary>
    public string DisplayName => string.IsNullOrEmpty(Description) 
        ? PortName 
        : $"{PortName} - {Description}";

    public override string ToString() => DisplayName;
}

/// <summary>
/// 串口连接状态
/// </summary>
public enum SerialPortConnectionStatus
{
    /// <summary>
    /// 未连接
    /// </summary>
    Disconnected = 0,

    /// <summary>
    /// 正在连接
    /// </summary>
    Connecting = 1,

    /// <summary>
    /// 已连接
    /// </summary>
    Connected = 2,

    /// <summary>
    /// 连接失败
    /// </summary>
    ConnectionFailed = 3,

    /// <summary>
    /// 正在断开连接
    /// </summary>
    Disconnecting = 4
}

/// <summary>
/// 串口连接配置
/// </summary>
public class SerialPortConnectionConfig
{
    /// <summary>
    /// 选择的串口名称
    /// </summary>
    public string SelectedPortName { get; set; } = string.Empty;

    /// <summary>
    /// 选择的协议类型
    /// </summary>
    public SerialPortProtocolType SelectedProtocolType { get; set; } = SerialPortProtocolType.CommercialProtocol;

    /// <summary>
    /// 连接状态
    /// </summary>
    public SerialPortConnectionStatus ConnectionStatus { get; set; } = SerialPortConnectionStatus.Disconnected;

    /// <summary>
    /// 最后连接时间
    /// </summary>
    public DateTime? LastConnectedTime { get; set; }

    /// <summary>
    /// 自动重连
    /// </summary>
    public bool AutoReconnect { get; set; } = true;

    /// <summary>
    /// 获取协议配置
    /// </summary>
    public SerialPortProtocolConfig GetProtocolConfig()
    {
        return SerialPortProtocolConfig.GetPredefinedProtocols()
            .FirstOrDefault(p => p.ProtocolType == SelectedProtocolType)
            ?? SerialPortProtocolConfig.GetPredefinedProtocols().First();
    }
}

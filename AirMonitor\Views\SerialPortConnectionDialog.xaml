<Window
    x:Class="AirMonitor.Views.SerialPortConnectionDialog"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:vm="clr-namespace:AirMonitor.ViewModels"
    Title="{Binding Title, FallbackValue='串口连接'}"
    Width="500"
    Height="550"
    MinWidth="450"
    MinHeight="550"
    vm:ViewModelLocator.AutoWireViewModel="True"
    Background="{DynamicResource {x:Static SystemColors.WindowBrushKey}}"
    Foreground="{DynamicResource {x:Static SystemColors.WindowTextBrushKey}}"
    ResizeMode="CanResize"
    ShowInTaskbar="False"
    WindowStartupLocation="CenterOwner"
    WindowStyle="SingleBorderWindow"
    mc:Ignorable="d">

    <Window.Resources>
        <!--  样式定义  -->
        <Style x:Key="GroupBoxStyle" TargetType="GroupBox">
            <Setter Property="Margin" Value="10,5" />
            <Setter Property="Padding" Value="10" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <Style x:Key="LabelStyle" TargetType="Label">
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="Margin" Value="0,5" />
            <Setter Property="MinWidth" Value="80" />
        </Style>

        <Style x:Key="ComboBoxStyle" TargetType="ComboBox">
            <Setter Property="Margin" Value="5" />
            <Setter Property="Padding" Value="5,3" />
            <Setter Property="VerticalAlignment" Value="Center" />
        </Style>

        <Style x:Key="ButtonStyle" TargetType="Button">
            <Setter Property="Margin" Value="5" />
            <Setter Property="Padding" Value="15,5" />
            <Setter Property="MinWidth" Value="80" />
        </Style>

        <Style x:Key="StatusTextStyle" TargetType="TextBlock">
            <Setter Property="Margin" Value="5" />
            <Setter Property="VerticalAlignment" Value="Center" />
            <Setter Property="FontWeight" Value="SemiBold" />
        </Style>

        <!--  连接状态颜色转换器  -->
        <Style
            x:Key="ConnectionStatusStyle"
            BasedOn="{StaticResource StatusTextStyle}"
            TargetType="TextBlock">
            <Style.Triggers>
                <DataTrigger Binding="{Binding ConnectionStatus}" Value="Disconnected">
                    <Setter Property="Foreground" Value="Gray" />
                </DataTrigger>
                <DataTrigger Binding="{Binding ConnectionStatus}" Value="Connecting">
                    <Setter Property="Foreground" Value="Orange" />
                </DataTrigger>
                <DataTrigger Binding="{Binding ConnectionStatus}" Value="Connected">
                    <Setter Property="Foreground" Value="Green" />
                </DataTrigger>
                <DataTrigger Binding="{Binding ConnectionStatus}" Value="ConnectionFailed">
                    <Setter Property="Foreground" Value="Red" />
                </DataTrigger>
                <DataTrigger Binding="{Binding ConnectionStatus}" Value="Disconnecting">
                    <Setter Property="Foreground" Value="Orange" />
                </DataTrigger>
            </Style.Triggers>
        </Style>
    </Window.Resources>

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>

        <!--  主要内容区域  -->
        <ScrollViewer Grid.Row="0" VerticalScrollBarVisibility="Auto">
            <StackPanel>
                <!--  串口选择  -->
                <GroupBox Header="串口选择" Style="{StaticResource GroupBoxStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <!--  串口选择  -->
                        <Label
                            Grid.Row="0"
                            Grid.Column="0"
                            Content="串口:"
                            Style="{StaticResource LabelStyle}" />
                        <ComboBox
                            Grid.Row="0"
                            Grid.Column="1"
                            DisplayMemberPath="DisplayName"
                            IsEnabled="{Binding IsPortSelectionEnabled}"
                            ItemsSource="{Binding AvailablePorts}"
                            SelectedItem="{Binding SelectedPort}"
                            Style="{StaticResource ComboBoxStyle}">
                            <ComboBox.ItemContainerStyle>
                                <Style TargetType="ComboBoxItem">
                                    <Setter Property="IsEnabled" Value="{Binding IsAvailable}" />
                                    <Style.Triggers>
                                        <DataTrigger Binding="{Binding IsAvailable}" Value="False">
                                            <Setter Property="Foreground" Value="Gray" />
                                            <Setter Property="FontStyle" Value="Italic" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding IsConnected}" Value="True">
                                            <Setter Property="Foreground" Value="Green" />
                                            <Setter Property="FontWeight" Value="Bold" />
                                        </DataTrigger>
                                    </Style.Triggers>
                                </Style>
                            </ComboBox.ItemContainerStyle>
                        </ComboBox>
                        <StackPanel Grid.Row="0" Grid.Column="2" Orientation="Horizontal">
                            <Button
                                Command="{Binding RefreshPortsCommand}"
                                Content="刷新"
                                Style="{StaticResource ButtonStyle}" />
                            <!-- 调试用测试按钮 -->
                            <Button
                                Margin="5,0,0,0"
                                Command="{Binding TestHotPlugCommand}"
                                Content="测试"
                                Style="{StaticResource ButtonStyle}"
                                ToolTip="手动触发热插拔检测（调试用）" />
                        </StackPanel>

                        <!--  连接状态  -->
                        <Label
                            Grid.Row="1"
                            Grid.Column="0"
                            Content="状态:"
                            Style="{StaticResource LabelStyle}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Style="{StaticResource ConnectionStatusStyle}"
                            Text="{Binding ConnectionStatusText}" />
                    </Grid>
                </GroupBox>

                <!--  协议选择  -->
                <GroupBox Header="协议配置" Style="{StaticResource GroupBoxStyle}">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <!--  协议选择  -->
                        <Label
                            Grid.Row="0"
                            Grid.Column="0"
                            Content="协议:"
                            Style="{StaticResource LabelStyle}" />
                        <ComboBox
                            Grid.Row="0"
                            Grid.Column="1"
                            DisplayMemberPath="Name"
                            IsEnabled="{Binding IsProtocolSelectionEnabled}"
                            ItemsSource="{Binding AvailableProtocols}"
                            SelectedItem="{Binding SelectedProtocol}"
                            Style="{StaticResource ComboBoxStyle}" />

                        <!--  协议详情  -->
                        <Label
                            Grid.Row="1"
                            Grid.Column="0"
                            Content="参数:"
                            Style="{StaticResource LabelStyle}" />
                        <TextBlock
                            Grid.Row="1"
                            Grid.Column="1"
                            Style="{StaticResource StatusTextStyle}">
                            <TextBlock.Text>
                                <MultiBinding StringFormat="波特率: {0}, 数据位: {1}, 停止位: {2}, 校验位: {3}">
                                    <Binding Path="SelectedProtocol.BaudRate" />
                                    <Binding Path="SelectedProtocol.DataBits" />
                                    <Binding Path="SelectedProtocol.StopBits" />
                                    <Binding Path="SelectedProtocol.Parity" />
                                </MultiBinding>
                            </TextBlock.Text>
                        </TextBlock>
                    </Grid>
                </GroupBox>

                <!--  连接选项  -->
                <GroupBox Header="连接选项" Style="{StaticResource GroupBoxStyle}">
                    <StackPanel>
                        <CheckBox
                            Margin="5"
                            Content="自动重连（串口重新插入时自动连接）"
                            IsChecked="{Binding AutoReconnect}" />
                    </StackPanel>
                </GroupBox>

                <!--  连接操作  -->
                <GroupBox Header="连接操作" Style="{StaticResource GroupBoxStyle}">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>

                        <Button
                            Grid.Column="1"
                            MinWidth="100"
                            Command="{Binding ConnectCommand}"
                            Content="{Binding ConnectButtonText}"
                            IsEnabled="{Binding IsConnectButtonEnabled}"
                            Style="{StaticResource ButtonStyle}" />
                    </Grid>
                </GroupBox>
            </StackPanel>
        </ScrollViewer>

        <!--  状态栏  -->
        <Border
            Grid.Row="1"
            Padding="10,5"
            Background="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"
            BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}"
            BorderThickness="0,1,0,0">
            <TextBlock
                Foreground="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}"
                Style="{StaticResource StatusTextStyle}"
                Text="{Binding StatusMessage}" />
        </Border>

        <!--  按钮区域  -->
        <Border
            Grid.Row="2"
            Padding="10"
            Background="{DynamicResource {x:Static SystemColors.ControlBrushKey}}"
            BorderBrush="{DynamicResource {x:Static SystemColors.ControlDarkBrushKey}}"
            BorderThickness="0,1,0,0">
            <StackPanel HorizontalAlignment="Right" Orientation="Horizontal">
                <Button
                    Command="{Binding CloseCommand}"
                    Content="关闭"
                    IsCancel="True"
                    Style="{StaticResource ButtonStyle}" />
            </StackPanel>
        </Border>
    </Grid>
</Window>

using System.Collections.ObjectModel;
using System.Windows.Input;
using CommunityToolkit.Mvvm.ComponentModel;
using CommunityToolkit.Mvvm.Input;
using AirMonitor.Models;
using AirMonitor.Services;

namespace AirMonitor.ViewModels;

/// <summary>
/// 串口连接对话框ViewModel
/// </summary>
public partial class SerialPortConnectionViewModel : ViewModelBase
{
    #region 字段

    private readonly ISerialPortService _serialPortService;

    #endregion

    #region 可观察属性

    /// <summary>
    /// 可用串口列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<SerialPortInfo> _availablePorts = new();

    /// <summary>
    /// 选择的串口
    /// </summary>
    [ObservableProperty]
    private SerialPortInfo? _selectedPort;

    /// <summary>
    /// 可用协议列表
    /// </summary>
    [ObservableProperty]
    private ObservableCollection<SerialPortProtocolConfig> _availableProtocols = new();

    /// <summary>
    /// 选择的协议
    /// </summary>
    [ObservableProperty]
    private SerialPortProtocolConfig? _selectedProtocol;

    /// <summary>
    /// 连接状态
    /// </summary>
    [ObservableProperty]
    private SerialPortConnectionStatus _connectionStatus = SerialPortConnectionStatus.Disconnected;

    /// <summary>
    /// 连接状态文本
    /// </summary>
    [ObservableProperty]
    private string _connectionStatusText = "未连接";

    /// <summary>
    /// 是否正在连接
    /// </summary>
    [ObservableProperty]
    private bool _isConnecting = false;

    /// <summary>
    /// 连接按钮文本
    /// </summary>
    [ObservableProperty]
    private string _connectButtonText = "连接";

    /// <summary>
    /// 是否启用连接按钮
    /// </summary>
    [ObservableProperty]
    private bool _isConnectButtonEnabled = true;

    /// <summary>
    /// 是否启用串口选择
    /// </summary>
    [ObservableProperty]
    private bool _isPortSelectionEnabled = true;

    /// <summary>
    /// 是否启用协议选择
    /// </summary>
    [ObservableProperty]
    private bool _isProtocolSelectionEnabled = true;

    /// <summary>
    /// 状态消息
    /// </summary>
    [ObservableProperty]
    private string _statusMessage = "请选择串口和协议";

    /// <summary>
    /// 自动重连
    /// </summary>
    [ObservableProperty]
    private bool _autoReconnect = true;

    #endregion

    #region 命令

    /// <summary>
    /// 连接/断开连接命令
    /// </summary>
    public ICommand ConnectCommand { get; }

    /// <summary>
    /// 刷新串口列表命令
    /// </summary>
    public ICommand RefreshPortsCommand { get; }

    /// <summary>
    /// 确定命令
    /// </summary>
    public ICommand OkCommand { get; }

    /// <summary>
    /// 取消命令
    /// </summary>
    public ICommand CancelCommand { get; }

    #endregion

    #region 构造函数

    public SerialPortConnectionViewModel(ISerialPortService serialPortService, ILoggingService loggingService)
        : base(loggingService)
    {
        _serialPortService = serialPortService ?? throw new ArgumentNullException(nameof(serialPortService));

        Title = "串口连接";

        // 初始化命令
        ConnectCommand = new AsyncRelayCommand(ExecuteConnectAsync, CanExecuteConnect);
        RefreshPortsCommand = new AsyncRelayCommand(ExecuteRefreshPortsAsync);
        OkCommand = new RelayCommand(ExecuteOk);
        CancelCommand = new RelayCommand(ExecuteCancel);

        // 订阅串口服务事件
        _serialPortService.ConnectionStatusChanged += OnConnectionStatusChanged;
        _serialPortService.PortListChanged += OnPortListChanged;

        // 初始化协议列表
        var protocols = SerialPortProtocolConfig.GetPredefinedProtocols();
        AvailableProtocols = new ObservableCollection<SerialPortProtocolConfig>(protocols);
        SelectedProtocol = protocols.FirstOrDefault();

        // 监听属性变化以更新命令状态
        PropertyChanged += OnPropertyChanged;
    }

    #endregion

    #region 重写方法

    protected override async Task OnInitializeAsync()
    {
        await ExecuteAsync(async () =>
        {
            LoggingService?.LogInformation("正在初始化串口连接对话框...");

            // 加载保存的连接配置
            var config = await _serialPortService.LoadConnectionConfigAsync();
            
            // 刷新串口列表
            await RefreshPortsAsync();

            // 恢复之前的选择
            RestoreConnectionConfig(config);

            // 启动串口监控
            _serialPortService.StartPortMonitoring();

            // 更新连接状态
            UpdateConnectionStatus(_serialPortService.ConnectionStatus);

            StatusMessage = "就绪";
            LoggingService?.LogInformation("串口连接对话框初始化完成");
        });
    }

    #endregion

    #region 命令实现

    /// <summary>
    /// 执行连接/断开连接
    /// </summary>
    private async Task ExecuteConnectAsync()
    {
        await ExecuteAsync(async () =>
        {
            if (ConnectionStatus == SerialPortConnectionStatus.Connected)
            {
                // 断开连接
                StatusMessage = "正在断开连接...";
                var success = await _serialPortService.DisconnectAsync();
                StatusMessage = success ? "连接已断开" : "断开连接失败";
            }
            else
            {
                // 建立连接
                if (SelectedPort == null || SelectedProtocol == null)
                {
                    StatusMessage = "请选择串口和协议";
                    return;
                }

                StatusMessage = $"正在连接到 {SelectedPort.PortName}...";
                var success = await _serialPortService.ConnectAsync(
                    SelectedPort.PortName, 
                    SelectedProtocol.ProtocolType);

                if (success)
                {
                    StatusMessage = $"已连接到 {SelectedPort.PortName}";
                    
                    // 保存连接配置
                    var config = new SerialPortConnectionConfig
                    {
                        SelectedPortName = SelectedPort.PortName,
                        SelectedProtocolType = SelectedProtocol.ProtocolType,
                        AutoReconnect = AutoReconnect
                    };
                    await _serialPortService.SaveConnectionConfigAsync(config);
                }
                else
                {
                    StatusMessage = "连接失败";
                }
            }
        });
    }

    /// <summary>
    /// 判断是否可以执行连接命令
    /// </summary>
    private bool CanExecuteConnect()
    {
        if (ConnectionStatus == SerialPortConnectionStatus.Connected)
            return true;

        var canExecute = SelectedPort != null && SelectedProtocol != null && !IsConnecting;

        // 添加调试信息
        LoggingService?.LogDebug("CanExecuteConnect: SelectedPort={0}, SelectedProtocol={1}, IsConnecting={2}, Result={3}",
            SelectedPort?.PortName ?? "null",
            SelectedProtocol?.Name ?? "null",
            IsConnecting,
            canExecute);

        return canExecute;
    }

    /// <summary>
    /// 执行刷新串口列表
    /// </summary>
    private async Task ExecuteRefreshPortsAsync()
    {
        await ExecuteAsync(async () =>
        {
            StatusMessage = "正在刷新串口列表...";
            await RefreshPortsAsync();
            StatusMessage = $"找到 {AvailablePorts.Count} 个串口";
        });
    }

    /// <summary>
    /// 执行确定
    /// </summary>
    private void ExecuteOk()
    {
        // 对话框将通过DialogResult关闭
        DialogResult = true;
    }

    /// <summary>
    /// 执行取消
    /// </summary>
    private void ExecuteCancel()
    {
        // 对话框将通过DialogResult关闭
        DialogResult = false;
    }

    #endregion

    #region 私有方法

    /// <summary>
    /// 刷新串口列表
    /// </summary>
    private async Task RefreshPortsAsync()
    {
        try
        {
            var ports = await _serialPortService.GetAvailablePortsAsync();
            
            // 保存当前选择
            var selectedPortName = SelectedPort?.PortName;
            
            // 更新列表
            AvailablePorts.Clear();
            foreach (var port in ports)
            {
                AvailablePorts.Add(port);
            }

            // 恢复选择
            if (!string.IsNullOrEmpty(selectedPortName))
            {
                SelectedPort = AvailablePorts.FirstOrDefault(p => p.PortName == selectedPortName);
            }

            LoggingService?.LogDebug("串口列表已刷新，找到 {Count} 个串口", ports.Count);
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "刷新串口列表失败");
            StatusMessage = "刷新串口列表失败";
        }
    }

    /// <summary>
    /// 恢复连接配置
    /// </summary>
    private void RestoreConnectionConfig(SerialPortConnectionConfig config)
    {
        try
        {
            // 恢复协议选择
            if (config.SelectedProtocolType != default)
            {
                SelectedProtocol = AvailableProtocols.FirstOrDefault(p => 
                    p.ProtocolType == config.SelectedProtocolType);
            }

            // 恢复串口选择
            if (!string.IsNullOrEmpty(config.SelectedPortName))
            {
                SelectedPort = AvailablePorts.FirstOrDefault(p => 
                    p.PortName == config.SelectedPortName);
            }

            // 恢复其他设置
            AutoReconnect = config.AutoReconnect;

            LoggingService?.LogDebug("连接配置已恢复");
        }
        catch (Exception ex)
        {
            LoggingService?.LogError(ex, "恢复连接配置失败");
        }
    }

    /// <summary>
    /// 更新连接状态
    /// </summary>
    private void UpdateConnectionStatus(SerialPortConnectionStatus status)
    {
        ConnectionStatus = status;
        IsConnecting = status == SerialPortConnectionStatus.Connecting || 
                      status == SerialPortConnectionStatus.Disconnecting;

        ConnectionStatusText = status switch
        {
            SerialPortConnectionStatus.Disconnected => "未连接",
            SerialPortConnectionStatus.Connecting => "正在连接...",
            SerialPortConnectionStatus.Connected => "已连接",
            SerialPortConnectionStatus.ConnectionFailed => "连接失败",
            SerialPortConnectionStatus.Disconnecting => "正在断开...",
            _ => "未知状态"
        };

        ConnectButtonText = status == SerialPortConnectionStatus.Connected ? "断开连接" : "连接";
        
        IsConnectButtonEnabled = !IsConnecting;
        IsPortSelectionEnabled = status == SerialPortConnectionStatus.Disconnected;
        IsProtocolSelectionEnabled = status == SerialPortConnectionStatus.Disconnected;

        // 刷新命令状态
        ((AsyncRelayCommand)ConnectCommand).NotifyCanExecuteChanged();
    }

    #endregion

    #region 属性变化处理

    /// <summary>
    /// 属性变化事件处理
    /// </summary>
    private void OnPropertyChanged(object? sender, System.ComponentModel.PropertyChangedEventArgs e)
    {
        // 当选择的串口或协议变化时，更新连接命令状态
        if (e.PropertyName == nameof(SelectedPort) || e.PropertyName == nameof(SelectedProtocol))
        {
            ((AsyncRelayCommand)ConnectCommand).NotifyCanExecuteChanged();
            LoggingService?.LogDebug("属性 {PropertyName} 变化，已更新连接命令状态", e.PropertyName);
        }
    }

    #endregion

    #region 事件处理

    /// <summary>
    /// 连接状态变化事件处理
    /// </summary>
    private void OnConnectionStatusChanged(object? sender, SerialPortConnectionStatusChangedEventArgs e)
    {
        // 在UI线程上更新状态
        System.Windows.Application.Current?.Dispatcher.Invoke(() =>
        {
            UpdateConnectionStatus(e.NewStatus);
            
            if (!string.IsNullOrEmpty(e.ErrorMessage))
            {
                StatusMessage = e.ErrorMessage;
            }
        });
    }

    /// <summary>
    /// 串口列表变化事件处理
    /// </summary>
    private void OnPortListChanged(object? sender, SerialPortListChangedEventArgs e)
    {
        // 在UI线程上更新串口列表
        System.Windows.Application.Current?.Dispatcher.Invoke(async () =>
        {
            await RefreshPortsAsync();
            
            if (e.AddedPorts.Any())
            {
                StatusMessage = $"检测到新串口: {string.Join(", ", e.AddedPorts)}";
            }
            else if (e.RemovedPorts.Any())
            {
                StatusMessage = $"串口已移除: {string.Join(", ", e.RemovedPorts)}";
            }
        });
    }

    #endregion

    #region 属性

    /// <summary>
    /// 对话框结果
    /// </summary>
    public bool? DialogResult { get; private set; }

    #endregion

    #region 清理

    protected override void Dispose(bool disposing)
    {
        if (disposing)
        {
            // 取消订阅事件
            _serialPortService.ConnectionStatusChanged -= OnConnectionStatusChanged;
            _serialPortService.PortListChanged -= OnPortListChanged;
            
            // 停止串口监控
            _serialPortService.StopPortMonitoring();
        }

        base.Dispose(disposing);
    }

    #endregion
}
